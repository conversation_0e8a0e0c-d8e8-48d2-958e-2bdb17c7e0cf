{"name": "amar-moneybag", "version": "1.0.0", "description": "একটি সম্পূর্ণ ব্যক্তিগত অর্থ ব্যবস্থাপনা অ্যাপ্লিকেশন", "main": "main.js", "scripts": {"start": "electron .", "dev": "NODE_ENV=development electron .", "build": "electron-builder", "build-win": "electron-builder --win", "build-mac": "electron-builder --mac", "build-linux": "electron-builder --linux", "dist": "npm run build", "pack": "electron-builder --dir", "postinstall": "electron-builder install-app-deps"}, "keywords": ["money-manager", "personal-finance", "expense-tracker", "income-tracker", "bangla", "electron"], "author": {"name": "<PERSON> <PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"electron": "^27.0.0", "electron-builder": "^24.6.4", "electron-reload": "^2.0.0-alpha.1"}, "dependencies": {"electron-updater": "^6.1.4"}, "build": {"appId": "com.fahimhaque.amar-moneybag", "productName": "আমার মানিব্যাগ", "directories": {"output": "dist"}, "files": ["**/*", "!**/node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}", "!**/node_modules/*/{test,__tests__,tests,powered-test,example,examples}", "!**/node_modules/*.d.ts", "!**/node_modules/.bin", "!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}", "!.editorconfig", "!**/._*", "!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}", "!**/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}", "!**/{appveyor.yml,.travis.yml,circle.yml}", "!**/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}"], "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}, {"target": "portable", "arch": ["x64", "ia32"]}], "icon": "assets/icon.ico", "requestedExecutionLevel": "asInvoker"}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}, {"target": "zip", "arch": ["x64", "arm64"]}], "icon": "assets/icon.icns", "category": "public.app-category.finance"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}, {"target": "rpm", "arch": ["x64"]}], "icon": "assets/icon.png", "category": "Office"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "আমার মানিব্যাগ"}, "dmg": {"title": "আমার মানিব্যাগ", "icon": "assets/icon.icns", "background": "assets/dmg-background.png", "window": {"width": 540, "height": 380}, "contents": [{"x": 140, "y": 200, "type": "file"}, {"x": 400, "y": 200, "type": "link", "path": "/Applications"}]}}, "repository": {"type": "git", "url": "https://github.com/fahimhaque/amar-moneybag.git"}, "bugs": {"url": "https://github.com/fahimhaque/amar-moneybag/issues"}, "homepage": "https://github.com/fahimhaque/amar-moneybag#readme"}
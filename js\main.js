const { app, BrowserWindow, ipcMain, Notification, shell, Menu, dialog } = require('electron');
const path = require('path');
const fs = require('fs');

// Keep a global reference of the window object
let mainWindow;

// Enable live reload for development
if (process.env.NODE_ENV === 'development') {
    require('electron-reload')(__dirname, {
        electron: path.join(__dirname, '..', 'node_modules', '.bin', 'electron'),
        hardResetMethod: 'exit'
    });
}

function createWindow() {
    // Create the browser window
    mainWindow = new BrowserWindow({
        width: 1400,
        height: 900,
        minWidth: 1200,
        minHeight: 700,
        icon: path.join(__dirname, 'assets', 'icon.png'), // Add your app icon here
        webPreferences: {
            nodeIntegration: true,
            contextIsolation: false,
            enableRemoteModule: true,
            webSecurity: false // Allow local file access
        },
        show: false, // Don't show until ready
        titleBarStyle: 'default',
        frame: true,
        resizable: true,
        maximizable: true,
        fullscreenable: true
    });

    // Load the app
    mainWindow.loadFile('index.html');

    // Show window when ready to prevent visual flash
    mainWindow.once('ready-to-show', () => {
        mainWindow.show();
        
        // Focus the window
        if (process.platform === 'darwin') {
            mainWindow.focus();
        }
    });

    // Handle window closed
    mainWindow.on('closed', () => {
        mainWindow = null;
    });

    // Handle external links
    mainWindow.webContents.setWindowOpenHandler(({ url }) => {
        shell.openExternal(url);
        return { action: 'deny' };
    });

    // Open DevTools in development
    if (process.env.NODE_ENV === 'development') {
        mainWindow.webContents.openDevTools();
    }

    // Set up menu
    createMenu();
}

function createMenu() {
    const template = [
        {
            label: 'ফাইল',
            submenu: [
                {
                    label: 'নতুন',
                    accelerator: 'CmdOrCtrl+N',
                    click: () => {
                        mainWindow.webContents.send('menu-new');
                    }
                },
                {
                    label: 'সেভ',
                    accelerator: 'CmdOrCtrl+S',
                    click: () => {
                        mainWindow.webContents.send('menu-save');
                    }
                },
                { type: 'separator' },
                {
                    label: 'এক্সপোর্ট',
                    submenu: [
                        {
                            label: 'JSON এক্সপোর্ট',
                            click: () => {
                                mainWindow.webContents.send('menu-export-json');
                            }
                        },
                        {
                            label: 'CSV এক্সপোর্ট',
                            click: () => {
                                mainWindow.webContents.send('menu-export-csv');
                            }
                        }
                    ]
                },
                { type: 'separator' },
                {
                    label: 'বন্ধ করুন',
                    accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
                    click: () => {
                        app.quit();
                    }
                }
            ]
        },
        {
            label: 'সম্পাদনা',
            submenu: [
                { label: 'আন্ডু', accelerator: 'CmdOrCtrl+Z', role: 'undo' },
                { label: 'রিডু', accelerator: 'Shift+CmdOrCtrl+Z', role: 'redo' },
                { type: 'separator' },
                { label: 'কাট', accelerator: 'CmdOrCtrl+X', role: 'cut' },
                { label: 'কপি', accelerator: 'CmdOrCtrl+C', role: 'copy' },
                { label: 'পেস্ট', accelerator: 'CmdOrCtrl+V', role: 'paste' }
            ]
        },
        {
            label: 'ভিউ',
            submenu: [
                { label: 'রিলোড', accelerator: 'CmdOrCtrl+R', role: 'reload' },
                { label: 'ফোর্স রিলোড', accelerator: 'CmdOrCtrl+Shift+R', role: 'forceReload' },
                { label: 'ডেভেলপার টুলস', accelerator: 'F12', role: 'toggleDevTools' },
                { type: 'separator' },
                { label: 'জুম ইন', accelerator: 'CmdOrCtrl+Plus', role: 'zoomIn' },
                { label: 'জুম আউট', accelerator: 'CmdOrCtrl+-', role: 'zoomOut' },
                { label: 'রিসেট জুম', accelerator: 'CmdOrCtrl+0', role: 'resetZoom' },
                { type: 'separator' },
                { label: 'ফুলস্ক্রিন', accelerator: 'F11', role: 'togglefullscreen' }
            ]
        },
        {
            label: 'সাহায্য',
            submenu: [
                {
                    label: 'সম্পর্কে',
                    click: () => {
                        dialog.showMessageBox(mainWindow, {
                            type: 'info',
                            title: 'সম্পর্কে',
                            message: 'আমার মানিব্যাগ',
                            detail: 'একটি সম্পূর্ণ ব্যক্তিগত অর্থ ব্যবস্থাপনা অ্যাপ্লিকেশন\n\nডেভেলপার: MD Fahim Haque\nভার্সন: 1.0.0'
                        });
                    }
                }
            ]
        }
    ];

    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);
}

// App event handlers
app.whenReady().then(() => {
    createWindow();

    app.on('activate', () => {
        if (BrowserWindow.getAllWindows().length === 0) {
            createWindow();
        }
    });
});

app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

// IPC handlers for notifications
ipcMain.handle('show-notification', async (event, options) => {
    try {
        const notification = new Notification({
            title: options.title || 'আমার মানিব্যাগ',
            body: options.body || '',
            icon: options.icon || path.join(__dirname, 'assets', 'icon.png'),
            sound: options.sound !== false, // Enable sound by default
            urgency: options.urgency || 'normal', // low, normal, critical
            timeoutType: options.timeoutType || 'default', // default, never
            silent: options.silent === true // Force silent if explicitly set
        });

        // Handle notification click
        notification.on('click', () => {
            if (mainWindow) {
                if (mainWindow.isMinimized()) {
                    mainWindow.restore();
                }
                mainWindow.focus();
                mainWindow.webContents.send('notification-clicked', options);
            }
        });

        // Handle notification close
        notification.on('close', () => {
            mainWindow?.webContents.send('notification-closed', options);
        });

        // Show the notification
        notification.show();

        return { success: true };
    } catch (error) {
        console.error('Notification error:', error);
        return { success: false, error: error.message };
    }
});

// Handle custom sound playing
ipcMain.handle('play-notification-sound', async (event, soundOptions) => {
    try {
        // For custom sounds, we'll use the renderer process
        // This handler can be extended to play system sounds
        mainWindow?.webContents.send('play-custom-sound', soundOptions);
        return { success: true };
    } catch (error) {
        console.error('Sound play error:', error);
        return { success: false, error: error.message };
    }
});

// Handle file operations
ipcMain.handle('save-file', async (event, data) => {
    try {
        const result = await dialog.showSaveDialog(mainWindow, {
            title: 'ডেটা সেভ করুন',
            defaultPath: 'money-manager-data.json',
            filters: [
                { name: 'JSON Files', extensions: ['json'] },
                { name: 'All Files', extensions: ['*'] }
            ]
        });

        if (!result.canceled && result.filePath) {
            fs.writeFileSync(result.filePath, JSON.stringify(data, null, 2));
            return { success: true, filePath: result.filePath };
        }

        return { success: false, canceled: true };
    } catch (error) {
        console.error('Save file error:', error);
        return { success: false, error: error.message };
    }
});

ipcMain.handle('load-file', async (event) => {
    try {
        const result = await dialog.showOpenDialog(mainWindow, {
            title: 'ডেটা লোড করুন',
            filters: [
                { name: 'JSON Files', extensions: ['json'] },
                { name: 'All Files', extensions: ['*'] }
            ],
            properties: ['openFile']
        });

        if (!result.canceled && result.filePaths.length > 0) {
            const data = fs.readFileSync(result.filePaths[0], 'utf8');
            return { success: true, data: JSON.parse(data) };
        }

        return { success: false, canceled: true };
    } catch (error) {
        console.error('Load file error:', error);
        return { success: false, error: error.message };
    }
});

// Handle app info
ipcMain.handle('get-app-info', async () => {
    return {
        version: app.getVersion(),
        name: app.getName(),
        platform: process.platform,
        arch: process.arch,
        electronVersion: process.versions.electron,
        nodeVersion: process.versions.node
    };
});

// Handle window focus
ipcMain.on('focus-window', () => {
    if (mainWindow) {
        if (mainWindow.isMinimized()) {
            mainWindow.restore();
        }
        mainWindow.focus();
        mainWindow.show();
    }
});

// Prevent navigation away from the app
app.on('web-contents-created', (event, contents) => {
    contents.on('will-navigate', (event, navigationUrl) => {
        const parsedUrl = new URL(navigationUrl);
        
        if (parsedUrl.origin !== 'file://') {
            event.preventDefault();
        }
    });
});
